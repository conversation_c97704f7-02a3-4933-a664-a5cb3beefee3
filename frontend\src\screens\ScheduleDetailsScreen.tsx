import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackScreenProps } from '@react-navigation/stack';
import { colors, spacing, borderRadius, shadows } from '../constants';
import { Text, Button, Icon } from '../components/atoms';
import { UserInfo } from '../components/molecules';
import { HomeStackParamList } from '../navigation/HomeStackNavigator';
import { useScheduleById } from '../hooks/useSchedules';

type Props = StackScreenProps<HomeStackParamList, 'ScheduleDetails'>;

const ScheduleDetailsScreen: React.FC<Props> = ({ route, navigation }) => {
  const { scheduleId } = route.params;
  const { data: schedule, isLoading, error } = useScheduleById(scheduleId);

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const formatDate = (timeString: string) => {
    return new Date(timeString).toLocaleDateString('en-US', {
      weekday: 'short',
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };

  const handleCall = (phoneNumber: string) => {
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const handleEmail = (email: string) => {
    Linking.openURL(`mailto:${email}`);
  };

  const handleClockIn = () => {
    // TODO: Implement clock in functionality
    console.log('Clock in for schedule:', scheduleId);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="body" color="textSecondary">Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !schedule) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="body" color="textSecondary">
            {error ? 'Error loading schedule' : 'Schedule not found'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Service Name */}
        <Text variant="h2" color="textPrimary" style={styles.serviceName}>
          {schedule.service_name || 'Service Name A'}
        </Text>

        {/* Client Info */}
        <View style={styles.clientSection}>
          <UserInfo
            name={schedule.client_name}
            size="large"
          />
        </View>

        {/* Date and Time */}
        <View style={styles.dateTimeSection}>
          <View style={styles.dateTimeRow}>
            <View style={styles.dateTimeItem}>
              <Icon name="calendar-outline" size={20} color="primary" />
              <Text variant="body" color="textPrimary" style={styles.dateTimeText}>
                {formatDate(schedule.start_time)}
              </Text>
            </View>
            <View style={styles.dateTimeItem}>
              <Icon name="time-outline" size={20} color="primary" />
              <Text variant="body" color="textPrimary" style={styles.dateTimeText}>
                {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
              </Text>
            </View>
          </View>
        </View>

        {/* Client Contact */}
        <View style={styles.section}>
          <Text variant="h3" color="textPrimary" style={styles.sectionTitle}>
            Client Contact:
          </Text>

          {schedule.client_email && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleEmail(schedule.client_email)}
            >
              <Icon name="mail" size={20} color="textSecondary" />
              <Text variant="body" color="textPrimary" style={styles.contactText}>
                {schedule.client_email}
              </Text>
            </TouchableOpacity>
          )}

          {schedule.client_phone && (
            <TouchableOpacity
              style={styles.contactItem}
              onPress={() => handleCall(schedule.client_phone)}
            >
              <Icon name="call" size={20} color="textSecondary" />
              <Text variant="body" color="textPrimary" style={styles.contactText}>
                {schedule.client_phone}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Address */}
        <View style={styles.section}>
          <Text variant="h3" color="textPrimary" style={styles.sectionTitle}>
            Address:
          </Text>
          <Text variant="body" color="textPrimary" style={styles.addressText}>
            {schedule.location.address}
          </Text>
          <Text variant="body" color="textPrimary" style={styles.addressText}>
            {schedule.location.city}, {schedule.location.state} {schedule.location.zip_code}
          </Text>
        </View>

        {/* Tasks */}
        {schedule.tasks && schedule.tasks.length > 0 && (
          <View style={styles.section}>
            <Text variant="h3" color="textPrimary" style={styles.sectionTitle}>
              Tasks:
            </Text>
            {schedule.tasks.map((task, index) => (
              <View key={task.id} style={styles.taskItem}>
                <Text variant="title" color="primary" style={styles.taskTitle}>
                  {task.name}
                </Text>
                <Text variant="body" color="textSecondary" style={styles.taskDescription}>
                  {task.description}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Service Notes */}
        {schedule.notes && (
          <View style={styles.section}>
            <Text variant="h3" color="textPrimary" style={styles.sectionTitle}>
              Service Notes
            </Text>
            <Text variant="body" color="textSecondary" style={styles.notesText}>
              {schedule.notes}
            </Text>
          </View>
        )}

        {/* Clock In Button */}
        {schedule.status === 'scheduled' && (
          <Button
            variant="primary"
            onPress={handleClockIn}
            fullWidth
            rounded
            style={styles.clockInButton}
          >
            Clock In Now
          </Button>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: spacing.screenPadding,
    gap: spacing.lg,
    paddingBottom: spacing.xxxl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  serviceName: {
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  clientSection: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  dateTimeSection: {
    marginBottom: spacing.lg,
  },
  dateTimeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing.md,
  },
  dateTimeItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray50,
    padding: spacing.md,
    borderRadius: borderRadius.md,
  },
  dateTimeText: {
    marginLeft: spacing.sm,
    flex: 1,
  },
  section: {
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    marginBottom: spacing.md,
    fontWeight: '600',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.sm,
  },
  contactText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  addressText: {
    lineHeight: 24,
  },
  taskItem: {
    backgroundColor: colors.gray50,
    padding: spacing.md,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
  },
  taskTitle: {
    marginBottom: spacing.xs,
    fontWeight: '600',
  },
  taskDescription: {
    lineHeight: 20,
  },
  notesText: {
    lineHeight: 20,
  },
  clockInButton: {
    marginTop: spacing.lg,
  },
});

export default ScheduleDetailsScreen;
