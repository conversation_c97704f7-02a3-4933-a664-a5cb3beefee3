import * as Location from 'expo-location';
import { Alert, Platform } from 'react-native';

export interface LocationData {
  latitude: number;
  longitude: number;
}

export interface LocationError {
  code: string;
  message: string;
}

/**
 * Request location permissions from the user
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error requesting location permission:', error);
    return false;
  }
};

/**
 * Check if location permissions are granted
 */
export const checkLocationPermission = async (): Promise<boolean> => {
  try {
    const { status } = await Location.getForegroundPermissionsAsync();
    return status === 'granted';
  } catch (error) {
    console.error('Error checking location permission:', error);
    return false;
  }
};

/**
 * Get current location with high accuracy
 */
export const getCurrentLocation = async (): Promise<LocationData> => {
  try {
    // For web platform, use browser geolocation API as fallback
    if (Platform.OS === 'web') {
      return await getWebLocation();
    }

    // Check if location services are enabled (mobile only)
    const isEnabled = await Location.hasServicesEnabledAsync();
    if (!isEnabled) {
      throw new LocationError('LOCATION_DISABLED', 'Location services are disabled. Please enable location services in your device settings.');
    }

    // Check permissions
    const hasPermission = await checkLocationPermission();
    if (!hasPermission) {
      const granted = await requestLocationPermission();
      if (!granted) {
        throw new LocationError('PERMISSION_DENIED', 'Location permission is required to clock in. Please grant location access in your device settings.');
      }
    }

    // Get current position with high accuracy
    const location = await Location.getCurrentPositionAsync({
      accuracy: Location.Accuracy.High,
      timeInterval: 5000,
      distanceInterval: 1,
    });

    return {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
    };
  } catch (error: any) {
    console.error('Error getting current location:', error);

    // Handle specific error types
    if (error instanceof LocationError) {
      throw error;
    }

    // Handle expo-location specific errors
    if (error.code === 'E_LOCATION_SERVICES_DISABLED') {
      throw new LocationError('LOCATION_DISABLED', 'Location services are disabled. Please enable location services in your device settings.');
    }

    if (error.code === 'E_LOCATION_UNAVAILABLE') {
      throw new LocationError('LOCATION_UNAVAILABLE', 'Unable to determine your location. Please try again.');
    }

    // Generic error
    throw new LocationError('UNKNOWN_ERROR', 'Failed to get your current location. Please try again.');
  }
};

/**
 * Get location using browser's geolocation API (web fallback)
 */
const getWebLocation = async (): Promise<LocationData> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new LocationError('NOT_SUPPORTED', 'Geolocation is not supported by this browser.'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        });
      },
      (error) => {
        console.error('Web geolocation error:', error);

        switch (error.code) {
          case error.PERMISSION_DENIED:
            reject(new LocationError('PERMISSION_DENIED', 'Location access denied. Please allow location access in your browser.'));
            break;
          case error.POSITION_UNAVAILABLE:
            reject(new LocationError('LOCATION_UNAVAILABLE', 'Location information is unavailable.'));
            break;
          case error.TIMEOUT:
            reject(new LocationError('TIMEOUT', 'Location request timed out. Please try again.'));
            break;
          default:
            reject(new LocationError('UNKNOWN_ERROR', 'An unknown error occurred while retrieving location.'));
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  });
};

/**
 * Show location error alert to user
 */
export const showLocationErrorAlert = (error: LocationError, onRetry?: () => void, onCancel?: () => void) => {
  const buttons: any[] = [
    {
      text: 'Cancel',
      style: 'cancel',
      onPress: onCancel,
    },
  ];

  if (onRetry) {
    buttons.push({
      text: 'Retry',
      onPress: onRetry,
    });
  }

  if (error.code === 'PERMISSION_DENIED' || error.code === 'LOCATION_DISABLED') {
    buttons.push({
      text: 'Settings',
      onPress: () => {
        // On mobile, this would open device settings
        // For now, just show an alert
        Alert.alert(
          'Open Settings',
          'Please go to your device settings and enable location permissions for this app.',
          [{ text: 'OK' }]
        );
      },
    });
  }

  Alert.alert(
    'Location Error',
    error.message,
    buttons
  );
};

/**
 * Custom error class for location-related errors
 */
class LocationError extends Error {
  code: string;

  constructor(code: string, message: string) {
    super(message);
    this.name = 'LocationError';
    this.code = code;
  }
}

export { LocationError };
